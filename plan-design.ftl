<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:tblPr>
        <w:tblStyle w:val="TableGrid"/>
        <w:tblW w:w="${styling.table.width}" w:type="${styling.table.widthType}"/>
        <w:tblBorders>
            <w:top w:val="${styling.table.borders.top}"/>
            <w:left w:val="${styling.table.borders.left}"/>
            <w:bottom w:val="${styling.table.borders.bottom}"/>
            <w:right w:val="${styling.table.borders.right}"/>
            <w:insideH w:val="${styling.table.borders.insideH}"/>
            <w:insideV w:val="${styling.table.borders.insideV}"/>
        </w:tblBorders>
    </w:tblPr>
    <w:tblGrid>
        <w:gridCol w:w="${styling.columns.benefit.width}"/>
        <#list carriers as carrier>
            <w:gridCol w:w="${styling.columns.carrier.width}"/>
        </#list>
    </w:tblGrid>

    <!-- Header Row -->
    <w:tr>
        <w:trPr>
            <w:trHeight w:val="${styling.rows.header.height}"/>
        </w:trPr>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                <w:shd w:val="clear" w:color="auto"
                       w:fill="${styling.rows.header.cell.backgroundColor}"/>
                <w:vAlign w:val="${styling.rows.header.cell.verticalAlign}"/>
            </w:tcPr>
            <w:p>
                <w:pPr>
                    <w:jc w:val="${styling.rows.header.text.alignment}"/>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:color w:val="${styling.rows.header.text.color}"/>
                        <w:sz w:val="${styling.rows.header.text.fontSize}"/>
                        <#if styling.rows.header.text.bold><w:b/></#if>
                    </w:rPr>
                    <w:t>Benefit</w:t>
                </w:r>
            </w:p>
        </w:tc>
        <#list carriers as carrier>
            <w:tc>
                <w:tcPr>
                    <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                    <w:shd w:val="clear" w:color="auto"
                           w:fill="${styling.rows.header.cell.backgroundColor}"/>
                    <w:vAlign w:val="${styling.rows.header.cell.verticalAlign}"/>
                </w:tcPr>
                <w:p>
                    <w:pPr>
                        <w:jc w:val="${styling.rows.header.text.alignment}"/>
                    </w:pPr>
                    <w:r>
                        <w:rPr>
                            <w:color w:val="${styling.rows.header.text.color}"/>
                            <w:sz w:val="${styling.rows.header.text.fontSize}"/>
                            <#if styling.rows.header.text.bold><w:b/></#if>
                        </w:rPr>
                        <w:t>
                            ${carrier}
                        </w:t>
                    </w:r>
                </w:p>
            </w:tc>
        </#list>
    </w:tr>

    <#-- Calculate total number of benefit rows globally for dynamic sizing -->
    <#assign totalBenefitRows = 0>
    <#list sections as section>
        <#assign totalBenefitRows = totalBenefitRows + section.benefits?size>
    </#list>

    <#-- Determine global sizing based on total benefit rows -->
    <#if totalBenefitRows gt 17>
        <#-- Reduced sizing for tables with many rows -->
        <#assign dynamicSectionHeight = (styling.rows.section.height?number * 0.85)?round>
        <#assign dynamicSectionCellWidth = (styling.rows.section.cell.width?number * 0.9)?round>
        <#assign dynamicSectionFontSize = (styling.rows.section.text.fontSize?number * 0.9)?round>
        <#assign dynamicRowHeight = (styling.rows.benefit.height?number * 0.7)?round>
        <#assign dynamicBenefitCellWidth = (styling.rows.benefit.benefitCell.width?number * 0.8)?round>
        <#assign dynamicCarrierCellWidth = (styling.rows.benefit.carrierCell.width?number * 0.8)?round>
        <#assign dynamicBenefitFontSize = (styling.rows.benefit.benefitText.fontSize?number * 0.85)?round>
        <#assign dynamicCarrierFontSize = (styling.rows.benefit.carrierText.fontSize?number * 0.85)?round>
    <#else>
        <#-- Normal sizing for tables with fewer rows -->
        <#assign dynamicSectionHeight = styling.rows.section.height>
        <#assign dynamicSectionCellWidth = styling.rows.section.cell.width>
        <#assign dynamicSectionFontSize = styling.rows.section.text.fontSize>
        <#assign dynamicRowHeight = styling.rows.benefit.height>
        <#assign dynamicBenefitCellWidth = styling.rows.benefit.benefitCell.width>
        <#assign dynamicCarrierCellWidth = styling.rows.benefit.carrierCell.width>
        <#assign dynamicBenefitFontSize = styling.rows.benefit.benefitText.fontSize>
        <#assign dynamicCarrierFontSize = styling.rows.benefit.carrierText.fontSize>
    </#if>

    <#list sections as section>
        <!-- Additional spacing between different sections for better visual separation -->

        <!-- Section Header Row -->
        <w:tr>
            <w:trPr>
                <w:trHeight w:val="${dynamicSectionHeight}"/>
            </w:trPr>
            <w:tc>
                <w:tcPr>
                    <w:tcW w:w="${dynamicSectionCellWidth}" w:type="dxa"/>
                    <w:gridSpan w:val="${carriers?size + 1}"/>
                    <w:shd w:val="clear" w:color="auto"
                           w:fill="${styling.rows.section.cell.backgroundColor}"/>
                    <w:vAlign w:val="${styling.rows.section.cell.verticalAlign}"/>
                </w:tcPr>
                <w:p>
                    <w:pPr>
                        <w:jc w:val="${styling.rows.section.text.alignment}"/>
                    </w:pPr>
                    <w:r>
                        <w:rPr>
                            <w:color w:val="${styling.rows.section.text.color}"/>
                            <w:sz w:val="${dynamicSectionFontSize}"/>
                            <#if styling.rows.section.text.bold><w:b/></#if>
                        </w:rPr>
                        <w:t>
                            ${section.name}
                        </w:t>
                    </w:r>
                </w:p>
            </w:tc>
        </w:tr>

        <#list section.benefits as benefit>
            <!-- Benefit Row -->
            <w:tr>
                <w:trPr>
                    <w:trHeight w:val="${dynamicRowHeight}"/>
                </w:trPr>
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${dynamicBenefitCellWidth}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign}"/>
                    </w:tcPr>
                    <w:p>
                        <w:pPr>
                            <w:jc w:val="${styling.rows.benefit.benefitText.alignment}"/>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.benefitText.color}"/>
                                <w:sz w:val="${dynamicBenefitFontSize}"/>
                                <#if styling.rows.benefit.benefitText.bold><w:b/></#if>
                            </w:rPr>
                            <w:t>
                                <#-- Additional spacing for benefit names -->
                                  ${benefit.name}
                            </w:t>
                        </w:r>
                    </w:p>
                </w:tc>
                <#list carriers as carrier>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="${dynamicCarrierCellWidth}" w:type="dxa"/>
                            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:jc w:val="${styling.rows.benefit.carrierText.alignment}"/>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                    <w:sz w:val="${dynamicCarrierFontSize}"/>
                                    <#if styling.rows.benefit.carrierText.bold><w:b/></#if>
                                </w:rPr>
                                <w:t>
                                    <#if benefit.values?? && carrier?? && benefit.values[carrier]??>
                                        ${benefit.values[carrier]}
                                    <#else>
                                        -
                                    </#if>
                                </w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </#list>
                <!-- Added spacing after carrier list to prevent data clashing -->

            </w:tr>
            <!-- Added spacing between rows for better visual separation -->

        </#list>
    </#list>
</w:tbl>
{%rawXml pageBreakXml}