<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:tblPr>
        <w:tblStyle w:val="TableGrid"/>
        <w:tblW w:w="${styling.table.width}" w:type="${styling.table.widthType}"/>
        <w:tblBorders>
            <w:top w:val="${styling.table.borders.top}"/>
            <w:left w:val="${styling.table.borders.left}"/>
            <w:bottom w:val="${styling.table.borders.bottom}"/>
            <w:right w:val="${styling.table.borders.right}"/>
            <w:insideH w:val="${styling.table.borders.insideH}"/>
            <w:insideV w:val="${styling.table.borders.insideV}"/>
        </w:tblBorders>
    </w:tblPr>
    
    <w:tblGrid>
        <w:gridCol w:w="${styling.columns.benefit.width}"/>
        <w:gridCol w:w="${styling.columns.benefit.width}"/>
        <#list carriers as carrier><w:gridCol w:w="${styling.columns.carrier.width}"/></#list>
    </w:tblGrid>

    <!-- Header Row -->
    <w:tr>
        <w:trPr>
            <w:trHeight w:val="1000"/> <!-- Increased header row height -->
        </w:trPr>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor}"/>
                <w:vAlign w:val="center"/> <!-- Vertical center -->
            </w:tcPr>
            <w:p>
                <w:pPr><w:jc w:val="${styling.rows.header.text.alignment}"/></w:pPr>
                <w:r>
                    <w:rPr>
                        <w:b/>
                        <w:color w:val="FFFFFF"/>
                        <w:sz w:val="28"/>
                    </w:rPr>
                    <w:t>Categories</w:t>
                </w:r>
            </w:p>
        </w:tc>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor}"/>
                <w:vAlign w:val="${styling.rows.header.cell.verticalAlign}"/>
            </w:tcPr>
            <w:p>
                <w:pPr><w:jc w:val="${styling.rows.header.text.alignment}"/></w:pPr>
                <w:r>
                    <w:rPr>
                        <w:color w:val="${styling.rows.header.text.color}"/>
                        <w:sz w:val="${styling.rows.header.text.fontSize}"/>
                        <#if styling.rows.header.text.bold><w:b/></#if>
                    </w:rPr>
                    <w:t></w:t>
                </w:r>
            </w:p>
        </w:tc>
        <#list carriers as carrier>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor}"/>
                <w:vAlign w:val="${styling.rows.header.cell.verticalAlign}"/>
            </w:tcPr>
            <w:p>
                <w:pPr><w:jc w:val="${styling.rows.header.text.alignment}"/></w:pPr>
                <w:r>
                    <w:rPr>
                        <w:color w:val="${styling.rows.header.text.color}"/>
                        <w:sz w:val="${styling.rows.header.text.fontSize}"/>
                        <#if styling.rows.header.text.bold><w:b/></#if>
                    </w:rPr>
                    <w:t>${carrier}</w:t>
                </w:r>
            </w:p>
        </w:tc>
        </#list>
    </w:tr>

    <!-- Data Rows -->
    <#list sections as section>
        <#list section.benefits as benefit>
            <#assign isFirst = benefit?index == 0>
            <w:tr>
                <w:trPr>
                    <w:trHeight w:val="600"/> <!-- Row spacing -->
                </w:trPr>
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.benefitCell.width}" w:type="dxa"/>
                        <#if isFirst><w:vMerge w:val="restart"/><#else><w:vMerge/></#if>
                        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign}"/>
                    </w:tcPr>
                    <w:p>
                        <w:pPr><w:jc w:val="${styling.rows.benefit.benefitText.alignment}"/></w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.benefitText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.benefitText.fontSize}"/>
                                <#if styling.rows.benefit.benefitText.bold><w:b/></#if>
                            </w:rPr>
                            <w:t><#if isFirst>${section.name}:</#if></w:t>
                        </w:r>
                    </w:p>
                </w:tc>
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.carrierCell.width}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                    </w:tcPr>
                    <w:p>
                        <w:pPr><w:jc w:val="${styling.rows.benefit.carrierText.alignment}"/></w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                <#if styling.rows.benefit.carrierText.bold><w:b/></#if>
                            </w:rPr>
                            <w:t>${benefit.name}</w:t>
                        </w:r>
                    </w:p>
                </w:tc>
                <#list carriers as carrier>
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.carrierCell.width}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                    </w:tcPr>
                    <w:p>
                        <w:pPr><w:jc w:val="${styling.rows.benefit.carrierText.alignment}"/></w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                <#if styling.rows.benefit.carrierText.bold><w:b/></#if>
                            </w:rPr>
                            <w:t>${benefit.values[carrier]}</w:t>
                        </w:r>
                    </w:p>
                </w:tc>
                </#list>
            </w:tr>
        </#list>
    </#list>
</w:tbl>
