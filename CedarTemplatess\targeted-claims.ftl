<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:tblPr>
        <w:tblStyle w:val="TableGrid"/>
        <w:tblW w:w="${styling.table.width!''}" w:type="${styling.table.widthType!''}"/>
    <w:tblBorders>
      <#-- Only render borders if not 'nil' -->
      <#if styling.table.borders.top?? && styling.table.borders.top != "nil">
        <w:top w:val="${styling.table.borders.top!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.left?? && styling.table.borders.left != "nil">
        <w:left w:val="${styling.table.borders.left!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.bottom?? && styling.table.borders.bottom != "nil">
        <w:bottom w:val="${styling.table.borders.bottom!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.right?? && styling.table.borders.right != "nil">
        <w:right w:val="${styling.table.borders.right!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.insideH?? && styling.table.borders.insideH != "nil">
        <w:insideH w:val="${styling.table.borders.insideH!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.insideV?? && styling.table.borders.insideV != "nil">
        <w:insideV w:val="${styling.table.borders.insideV!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
    </w:tblBorders>
    </w:tblPr>

    <w:tblGrid>
        <w:gridCol w:w="3000"/>
        <#list carriers as carrier>
            <w:gridCol w:w="2500"/>
        </#list>
    </w:tblGrid>

    <!-- Header Row - Much Taller like in first image -->
    <w:tr>
        <w:trPr>
            <w:trHeight w:val="1000"/> <!-- Increased from 600 to 1200 for bigger header -->
        </w:trPr>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="3000" w:type="dxa"/>
                <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor}"/> <!-- Dark Blue -->
                <w:vAlign w:val="center"/>
            </w:tcPr>
            <w:p/>
        </w:tc>
        <#list carriers as carrier>
            <w:tc>
                <w:tcPr>
                    <w:tcW w:w="2500" w:type="dxa"/>
                    <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor}"/> <!-- Dark Blue -->
                    <w:vAlign w:val="center"/>
                </w:tcPr>
                <w:p>
                    <w:pPr>
                        <w:jc w:val="center"/>
                    </w:pPr>
                    <w:r>
                        <w:rPr>
                            <w:b/>
                            <w:color w:val="FFFFFF"/>
                            <w:sz w:val="36"/> <!-- Increased from 28 to 48 for larger text -->
                        </w:rPr>
                        <w:t>${carrier}</w:t>
                    </w:r>
                </w:p>
            </w:tc>
        </#list>
    </w:tr>

    <!-- Targeted Claims Row - No background color, white background -->
    <w:tr>
        <w:trPr>
            <w:trHeight w:val="800"/> <!-- Increased height for better proportion -->
        </w:trPr>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="3000" w:type="dxa"/>
                <w:shd w:val="clear" w:color="auto"/>
                <w:vAlign w:val="center"/>
            </w:tcPr>
            <w:p>
                <w:pPr>
                    <w:jc w:val="center"/> <!-- Changed to center align for proper alignment -->
                    <w:spacing w:before="120" w:after="120"/> <!-- Add padding -->
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:b/> <!-- Added bold -->
                        <w:color w:val="FFFFFF"/> <!-- White text -->
                        <w:sz w:val="30"/> <!-- Larger text size -->
                    </w:rPr>
                    <w:t>Targeted Claims $</w:t>
                </w:r>
            </w:p>
        </w:tc>
        <#list sections as section>
            <w:tc>
                <w:tcPr>
                    <w:tcW w:w="2500" w:type="dxa"/>
                    <!-- No background color - white background like first image -->
                    <w:vAlign w:val="center"/>
                </w:tcPr>
                <w:p>
                    <w:pPr>
                        <w:jc w:val="center"/> <!-- Changed to center align for proper alignment -->
                        <w:spacing w:before="120" w:after="120"/> <!-- Add padding -->
                    </w:pPr>
                    <w:r>
                        <w:rPr>
                            <w:b/> <!-- Added bold -->
                            <w:color w:val="FFFFFF"/> <!-- Changed to white text -->
                            <w:sz w:val="30"/> <!-- Matching size with label -->
                        </w:rPr>
                        <w:t>${section.amount}</w:t>
                    </w:r>
                </w:p>
            </w:tc>
        </#list>
    </w:tr>
</w:tbl>