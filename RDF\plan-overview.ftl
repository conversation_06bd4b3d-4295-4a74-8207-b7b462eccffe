<w:tbl>
  <w:tblPr>
    <w:tblStyle w:val="TableGrid"/>
    <w:tblW w:w="11000" w:type="dxa"/>  <#-- Table width -->
  </w:tblPr>
  <#list planOverview as item>
    <w:tr>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="11000" w:type="dxa"/>  <#-- Full width for single column -->
          <w:noWrap/>  <#-- Prevent text wrapping -->
          <w:vAlign w:val="center"/>  <#-- Vertical alignment for cell -->
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:spacing w:before="150" w:after="150"/>
            <w:jc w:val="left"/>  <#-- Left align the paragraph -->
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:rFonts w:ascii="Trebuchet MS" w:hAnsi="Trebuchet MS"/>
              <w:sz w:val="30"/>  <#-- Smaller bullet size -->
              <w:color w:val="FFFFFF"/>
              <w:b/>
              <w:position w:val="3"/>  <#-- Raise bullet slightly -->
            </w:rPr>
            <w:t>●</w:t>
          </w:r>
          <w:r>
            <w:rPr>
              <w:rFonts w:ascii="Trebuchet MS" w:hAnsi="Trebuchet MS"/>
              <w:sz w:val="46"/>  <#-- Font size for text -->
              <w:color w:val="FFFFFF"/>
              <w:b/>
            </w:rPr>
            <w:t xml:space="preserve">  ${item?replace(' & ', ' & ')}</w:t>  <#-- Added space before text -->
          </w:r>
        </w:p>
      </w:tc>
    </w:tr>
  </#list>
</w:tbl>