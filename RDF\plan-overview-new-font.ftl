<w:tbl>
  <w:tblPr>
    <w:tblStyle w:val="TableGrid"/>
    <w:tblW w:w="11000" w:type="dxa"/>  <!-- Table width -->
  </w:tblPr>
  <#list planOverview as item>
    <w:tr>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="11000" w:type="dxa"/>  <!-- Full width for single column -->
          <w:noWrap/>  <!-- Prevent text wrapping -->
          <w:vAlign w:val="center"/>  <!-- Vertical alignment for cell -->
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:spacing w:before="150" w:after="150"/>
            <w:jc w:val="left"/>  <!-- Left align the paragraph -->
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:rFonts w:ascii="Trebuchet MS"/>  <!-- Trebuchet MS font -->
              <w:sz w:val="38"/>  <!-- Smaller bullet size -->
              <w:color w:val="FFFFFF"/>
              <w:b/>
              <w:position w:val="3"/>  <!-- Raise bullet slightly -->
            </w:rPr>
            <w:t>●</w:t>
          </w:r>
          <w:r>
            <w:rPr>
              <w:rFonts w:ascii="Trebuchet MS"/>  <!-- Trebuchet MS font -->
              <w:sz w:val="56"/>  <!-- Font size for text -->
              <w:color w:val="FFFFFF"/>
              <w:b/>
            </w:rPr>
            <w:t xml:space="preserve">  ${item?cap_first?replace(' & ', ' &amp; ')}</w:t>  <!-- Capitalized first letter and proper XML encoding -->
          </w:r>
        </w:p>
      </w:tc>
    </w:tr>
  </#list>
</w:tbl>